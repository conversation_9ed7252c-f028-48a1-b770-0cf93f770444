import {
  <PERSON>mpo<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
  ViewChild,
  ElementRef,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  Validators,
  ReactiveFormsModule,
} from '@angular/forms';
import { Router } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { catchError, finalize, of, Subject, takeUntil } from 'rxjs';
import { environment } from '../../../../../environments/environment';

// Shared imports
import { FormBuilderComponent } from '@shared/components/form-builder/form-builder.component';
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';
import { PageHeaderComponent } from '@shared/components/page-header/page-header.component';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';
import { ErrorModalService } from '@core/services/error-modal.service';
import { CustomValidators } from '@shared/services/custom-validators.service';
import { FileUploadService } from '@shared/services/file.service';

// Core imports
import {
  UserManagementServiceProxy,
  UserProfileResponseDtoBaseResponse,
} from '@core/api/api.generated';

// Enums and interfaces
import { InputType } from '@shared/enum/input-type.enum';
import { ButtonTypeEnum } from '@core/enums/icon-enum';
import { IControlOption } from '@shared/interfaces/i-control-option';
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';

// Validators
import {
  saudiIbanValidator,
  saudiPassportValidator,
  saudiMobileValidator,
} from '@shared/validators/saudi-validators';
import { saudiPhoneValidator } from '@shared/validators/saudi-phone.validator';
import { UserManagementService } from '@shared/services/users/user-management.service';
import { AttachmentModule } from '@shared/enum/AttachmentModule';

@Component({
  selector: 'app-update-user-profile',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    TranslateModule,
    FormBuilderComponent,
    BreadcrumbComponent,
    PageHeaderComponent,
    CustomButtonComponent,
  ],
  templateUrl: './update-user-profile.component.html',
  styleUrls: ['./update-user-profile.component.scss'],
})
export class UpdateUserProfileComponent implements OnInit, OnDestroy {
  @ViewChild('photoFileInput') photoFileInput!: ElementRef<HTMLInputElement>;

  private destroy$ = new Subject<void>();

  userProfileForm!: FormGroup;
  formControls: IControlOption[] = [];
  breadcrumbItems: IBreadcrumbItem[] = [];

  isLoading = false;
  isFormSubmitted = false;
  currentUserData: any = null;

  // Photo display properties
  currentPhotoUrl: string = '';
  currentPhotoPath: string = '';
  currentPhotoFileId: number | null = null;
  userStatus: string = '';
  userRoles: string[] = [];

  // Enums for template
  ButtonTypeEnum = ButtonTypeEnum;

  constructor(
    private formBuilder: FormBuilder,
    private router: Router,
    private userManagementService: UserManagementServiceProxy,
    private userService: UserManagementService,
    private errorModalService: ErrorModalService,
    private fileUploadService: FileUploadService
  ) {
    this.initializeBreadcrumbs();
  }

  ngOnInit(): void {
    this.initializeForm();
    this.setupFormControls();
    this.loadCurrentUserData();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeBreadcrumbs(): void {
    // this.breadcrumbItems = [
    //   {
    //     label: 'COMMON.HOME',
    //     url: '/admin/dashboard',
    //   },
    //   {
    //     label: 'USER_PROFILE.PAGE_TITLE',
    //     disabled: true,
    //   },
    // ];

     this.breadcrumbItems = [
      {
        label: 'USER_PROFILE.UPDATE_PROFILE',
        url: '/admin/user-management'
      },
      {
        label: 'USER_PROFILE.PAGE_TITLE',
        disabled: true
      }
    ];
  }

  private initializeForm(): void {
    this.userProfileForm = this.formBuilder.group({
      name: ['', [Validators.required, Validators.minLength(2), Validators.maxLength(255)]],
      email: ['', [Validators.required, CustomValidators.strictEmail, Validators.maxLength(255)]],
      userName: ['', [Validators.required]], //, saudiMobileValidator() Full mobile for API
      iban: ['', [saudiIbanValidator()]], // Optional field with Saudi IBAN validation
      nationality: ['',[Validators.maxLength(100)]],  // Optional field
      cv: [''],
      passportNo: ['', [saudiPassportValidator()]],
      personalPhoto: [''],
      countryCode: ['+966', [Validators.required]], // Fixed to +966 for Saudi numbers
    });
  }

  private setupFormControls(): void {
    this.formControls = [
      // Basic Information Section
      {
        formControlName: 'name',
        type: InputType.Text,
        id: 'name',
        name: 'name',
        label: 'USER_PROFILE.NAME',
        placeholder: 'USER_PROFILE.NAME_PLACEHOLDER',
        isRequired: true,
        class: 'col-md-6',
        maxLength: 255,
      },
      {
        formControlName: 'email',
        type: InputType.Email,
        id: 'email',
        name: 'email',
        label: 'USER_PROFILE.EMAIL',
        placeholder: 'USER_PROFILE.EMAIL_PLACEHOLDER',
        isRequired: true,
        class: 'col-md-6',
        maxLength: 255,
      },
      {
        formControlName: 'countryCode',
        type: InputType.Text,
        id: 'countryCode',
        name: 'countryCode',
        label: 'USER_PROFILE.COUNTRY_CODE',
        placeholder: 'USER_PROFILE.COUNTRY_CODE_PLACEHOLDER',
        isRequired: true,
        class: 'col-1',
        maxLength: 5,
        isReadonly: true, // Fixed value, read-only
      },
      {
        formControlName: 'userName',
        type: InputType.Text,
        id: 'userName',
        name: 'userName',
        label: 'USER_PROFILE.MOBILE',
        placeholder: 'USER_PROFILE.MOBILE_PLACEHOLDER',
        isRequired: true,
        class: 'col-md-5',
        isReadonly: true,
      },

      // Additional Information
      {
        formControlName: 'iban',
        type: InputType.Mixed,
        id: 'iban',
        name: 'iban',
        label: 'USER_PROFILE.IBAN',
        placeholder: 'USER_PROFILE.IBAN_PLACEHOLDER',
        isRequired: false,
        class: 'col-md-6',
        maxLength: 34,
      },
      {
        formControlName: 'nationality',
        type: InputType.Text,
        id: 'nationality',
        name: 'nationality',
        label: 'USER_PROFILE.NATIONALITY',
        placeholder: 'USER_PROFILE.NATIONALITY_PLACEHOLDER',
        isRequired: false,
        class: 'col-md-6',
        maxLength: 100,
      },
      {
        formControlName: 'passportNo',
        type: InputType.Mixed,
        id: 'passportNo',
        name: 'passportNo',
        label: 'USER_PROFILE.PASSPORT_NO',
        placeholder: 'USER_PROFILE.PASSPORT_NO_PLACEHOLDER',
        isRequired: false,
        class: 'col-md-6',
        maxLength: 20,
      },

      // Document Upload
      {
        formControlName: 'cv',
        type: InputType.file,
        id: 'cv',
        name: 'cv',
        label: 'USER_PROFILE.CV',
        placeholder: '',
        isRequired: false,
        class: 'col-md-6',
        allowedTypes: ['pdf', 'docx'],
        max: 10,
        moduleId : AttachmentModule.User
      },
    ];
  }

  loadCurrentUserData(): void {
    this.isLoading = true;

    this.userManagementService
      .getUserProfile(undefined)
      .pipe(
        takeUntil(this.destroy$),
        catchError((error) => {
          console.error('Error loading user profile:', error);
          this.errorModalService.showError('USER_PROFILE.LOAD_ERROR');
          return of(null);
        }),
        finalize(() => {
          this.isLoading = false;
        })
      )
      .subscribe((response) => {
        if (response) {
          this.populateForm(response);
          console.log('User data:', response);
        }
      });
  }

  private populateForm(response: UserProfileResponseDtoBaseResponse): void {
    this.currentUserData = response;

    try {
      const user = response.data;

      this.userProfileForm.patchValue({
        name: user.fullName || '',
        email: user.email || '',
        userName: user.userName || '',
        iban: user.iban || '',
        nationality: user.nationality || '',
        passportNo: user.passportNo || '',
        cv: user.cvFile ,
      });
      this.userProfileForm.get('userName')?.disable();

      // Populate display properties for photo, status, and roles
      this.currentPhotoUrl = this.currentUserData?.data?.personalPhoto?.filePath?.length > 0 ? this.currentUserData?.data?.personalPhoto?.filePath : '';
      this.currentPhotoFileId = this.currentUserData?.data?.personalPhoto?.id;
      this.userStatus = user.isActive ? 'Active' : 'Inactive';
      this.userRoles = Array.isArray(user.roles) ? user.roles : [];
      const CvFile = this.currentUserData.data.cvFile;
      this.formControls.find(
        (c) => c.formControlName === 'cv'
      )!.initialFiles = [CvFile];


      console.log('Form populated successfully with user data:', user);
    } catch (error) {
      console.error('Error populating form:', error);
      this.errorModalService.showError('USER_PROFILE.POPULATE_ERROR');
    }
  }

  // Get user photo URL with fallback to default avatar
  getUserPhotoUrl(): string {
    // Prioritize the currentPhotoUrl if it's been updated
    if (this.currentPhotoUrl) {
      // If the photo is a full URL, use it directly
      if (this.currentPhotoUrl.startsWith('http')) {
        return this.currentPhotoUrl;
      }
      // If it's a relative path, construct the full URL using environment API URL
      const photoPath = this.currentPhotoUrl.startsWith('/')
        ? this.currentPhotoUrl
        : `/${this.currentPhotoUrl}`;
      return `${environment.apiUrl}${photoPath}`;
    }

    // Fall back to original user data photo
    if (this.currentUserData?.data?.personalPhoto?.filePath) {
      // If the photo is a full URL, use it directly
      if (this.currentUserData?.data?.personalPhoto?.filePath.startsWith('http')) {
        return this.currentUserData?.data?.personalPhoto?.filePath;
      }

      // If it's a relative path, construct the full URL using environment API URL
      const photoPath = this.currentUserData?.data?.personalPhoto?.filePath.startsWith(
        '/'
      )
        ? this.currentUserData?.data?.personalPhoto?.filePath
        : `/${this.currentUserData?.data?.personalPhoto?.filePath}`;

      return `${environment.apiUrl}${photoPath}`;
    }

    // Fallback to default avatar
    return 'assets/images/avatar-user.png';
  }

  // Handle image load error - fallback to default avatar
  onImageError(event: any): void {
    event.target.src = 'assets/images/avatar-user.png';
  }

  onValueChange(_event: any, _control: IControlOption): void {
    // Handle value changes if needed
  }

  onKeyPressed(_event: any, _control: IControlOption): void {
    // Handle key press events if needed
  }

  onDropdownChange(_event: any, _control: IControlOption): void {
    // Handle dropdown changes if needed
  }

  triggerPhotoUpload(): void {
    this.photoFileInput.nativeElement.click();
  }

  onPhotoUpload(event: any): void {
    const file = event.target.files[0];
    if (file) {
      // Validate file type
      const allowedTypes = ['jpg', 'jpeg', 'png'];
      const fileExtension = file.name.split('.').pop()?.toLowerCase();

      if (!fileExtension || !allowedTypes.includes(fileExtension)) {
        this.errorModalService.showError('USER_PROFILE.INVALID_PHOTO_TYPE');
        // Reset the file input
        this.photoFileInput.nativeElement.value = '';
        return;
      }

      // Validate file size (max 2MB)
      const maxSize = 2 * 1024 * 1024; // 2MB
      if (file.size > maxSize) {
        this.errorModalService.showError('USER_PROFILE.PHOTO_TOO_LARGE');
        // Reset the file input
        this.photoFileInput.nativeElement.value = '';
        return;
      }

      // Show loading state
      this.isLoading = true;

      // Upload file using the file upload service
      this.fileUploadService.uploadFileToMinIO(file, AttachmentModule.User).subscribe({
        next: (response: any) => {
          if (response && response.data && response.data.url) {
            // Update the current photo URL for immediate display
            this.currentPhotoUrl = response.data.url;
            this.currentPhotoFileId = response.data.id;

            // Update the user data with new photo URL
            if (this.currentUserData && this.currentUserData.data) {
              this.currentUserData.data.personalPhoto.filePath = response.data.url;
              this.userProfileForm
                .get('personalPhotoPath')
                ?.setValue(response.data.url);
            }

            console.log('Photo uploaded successfully:', response.data.url, 'File ID:', response.data.id);
            this.errorModalService.showSuccess(
              'USER_PROFILE.PHOTO_UPLOAD_SUCCESS'
            );
          }
        },
        error: (error) => {
          console.error('Error uploading photo:', error);
          // this.errorModalService.showError('USER_PROFILE.PHOTO_UPLOAD_ERROR');
        },
        complete: () => {
          this.isLoading = false;
          // Reset the file input for future uploads
          this.photoFileInput.nativeElement.value = '';
        },
      });
    }
  }

  onFileUploaded(event: any): void {
    const { file, control } = event;
    if (file && control) {
      const controlName = control.formControlName;
      // Set the file in the form control
      this.userProfileForm.get(controlName)?.setValue(file);
      console.log(`File uploaded for ${controlName}:`, file);
    }
  }

  onSubmit(): void {
    console.log('Form submitted');

    if (this.isLoading) {
      return;
    }

    this.isFormSubmitted = true;

    // Validate form and proceed with submission
    if (this.userProfileForm.valid) {
      this.isLoading = true;

      // Prepare form data for API call
      const formData = this.prepareFormData();

      // Call update profile API with proper error handling
      this.userManagementService
        .updateUserProfile(formData)
        .pipe(
          takeUntil(this.destroy$),
          catchError((error) => {
            console.error('Error updating user profile:', error);
            this.errorModalService.showError('USER_PROFILE.UPDATE_ERROR');
            return of(null);
          }),
          finalize(() => {
            this.isLoading = false;
          })
        )
        .subscribe((response) => {
          if (response && response.successed) {
            // Show success message and navigate
            this.errorModalService.showSuccess('USER_PROFILE.UPDATE_SUCCESS');
            this.router.navigate(['/admin/user-management/my-profile']);
          }
        });
    } else {
      // Mark all fields as touched to show validation errors
      this.markFormGroupTouched(this.userProfileForm);
    }
  }
private markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();

      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      }
    });
  }
  onCancel(): void {
    this.router.navigate(['/admin/dashboard']);
  }

  onChangePassword(): void {
    this.router.navigate(['/admin/change-password']);
  }

  private prepareFormData(): any {
    const formValue = this.userProfileForm.value;
    const userId = localStorage.getItem('userId');
    // Prepare data according to API requirements
    return {
      personalPhotoFileId: this.currentPhotoFileId,
      Email: formValue.email,
      IBAN: formValue.iban || "",
      Nationality: formValue.nationality || "",
      PassportNo: formValue.passportNo || "",
      FullName: formValue.name,
      UserName: this.currentUserData?.data?.userName || "",
      Id: Number(userId) || 0,
      cvFileId : formValue?.cv?.id,
      countryCode: formValue.countryCode,
    };
  }
}
