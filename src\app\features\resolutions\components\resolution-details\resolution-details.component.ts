import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatDividerModule } from '@angular/material/divider';
import { MatChipsModule } from '@angular/material/chips';

// Core imports
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';
import { SizeEnum } from '@shared/enum/size-enum';
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';

// API imports
import {
  ResolutionsServiceProxy,
  SingleResolutionResponse,
  ResolutionStatusEnum,
  RejectResolutionCommand,
  ResolutionItemDto,
} from '@core/api/api.generated';
import { TokenService } from '../../../auth/services/token.service';
import Swal from 'sweetalert2';
import { ConflictsPopupComponent } from '../conflicts-popup/conflicts-popup.component';
import { MatDialog } from '@angular/material/dialog';
import { AttachmentCardComponent } from '../attachment-card/attachment-card.component';
import { TimelineComponent } from '../timeline/timeline.component';
import { ResolutionStatus } from '@shared/enum/resolution-status';
import { ResolutionService } from '@core/services/resolution.service';
import { DateHijriConverterPipe } from '@shared/pipes/dateHijriConverter/dateHijriConverter.pipe';
import { ErrorModalService } from '@core/services/error-modal.service';
import {
  ConflictMembersDialogComponent,
  ConflictMembersDialogData,
} from '../edit-resolution/conflict-members-dialog/conflict-members-dialog.component';

@Component({
  selector: 'app-resolution-details',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatButtonModule,
    MatCardModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatDividerModule,
    MatChipsModule,
    BreadcrumbComponent,
    CustomButtonComponent,
    AttachmentCardComponent,
    TimelineComponent,
    DateHijriConverterPipe,
  ],
  templateUrl: './resolution-details.component.html',
  styleUrls: ['./resolution-details.component.scss'],
})
export class ResolutionDetailsComponent implements OnInit {
  // Data properties
  resolution: any | null = null;
  resolutionId: number = 0;
  fundId: number = 0;
  fundName: string = '';

  // Loading and error states
  isLoading = false;
  hasError = false;
  errorMessage = '';

  // Role-based access control
  userRole: string = '';
  canConfirmReject = false;
  canSendToVote = false;
  canViewDetails = false;

  // UI state
  breadcrumbSizeEnum = SizeEnum;
  buttonTypeEnum = ButtonTypeEnum;
  iconEnum = IconEnum;

  // Breadcrumb configuration
  breadcrumbItems: IBreadcrumbItem[] = [];
  isExpanded: boolean = true;
  isExpandedItem: boolean = true;
  isExpandedAction: boolean = true;
  resolutionStatus: any | null = null;
  buttonEnum = ButtonTypeEnum;
  IconEnum = IconEnum;
  resolutionStatusEnum = ResolutionStatus;
  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private resolutionsProxy: ResolutionsServiceProxy,
    public tokenService: TokenService,
    private translateService: TranslateService,
    private dialog: MatDialog,
    private resolutionService: ResolutionService,
    private errorModalService: ErrorModalService
  ) {}

  ngOnInit(): void {
    this.initializeComponent();
  }

  private initializeComponent(): void {
    // Get route parameters
    this.route.params.subscribe((params) => {
      this.resolutionId = +params['id'];

      // Get fundId from query params or route params
      this.route.queryParams.subscribe((queryParams) => {
        this.fundId = +queryParams['fundId'] || +params['fundId'] || 0;

        // Always setup breadcrumbs, even if fundId is invalid
        this.setupBreadcrumbs();

        if (this.resolutionId && this.fundId) {
          this.initializeRoleBasedAccess();
          this.loadResolutionDetails();
        } else {
          // Still initialize role-based access for basic functionality
          this.initializeRoleBasedAccess();
          this.handleError('RESOLUTIONS.INVALID_PARAMETERS');
        }
      });
    });
  }

  private initializeRoleBasedAccess(): void {
    // Determine user role based on permissions
    if (this.tokenService.hasRole('fundmanager')) {
      this.userRole = 'fundmanager';
      this.canViewDetails = true;
      this.canConfirmReject = true; // Fund manager can confirm/reject waiting for confirmation resolutions
      this.canSendToVote = false;
    } else if (
      this.tokenService.hasRole('legalcouncil') ||
      this.tokenService.hasRole('boardsecretary')
    ) {
      this.userRole = 'legalcouncil';
      this.canViewDetails = true;
      this.canConfirmReject = false; // Legal council/board secretary complete data, don't confirm/reject
      this.canSendToVote = true; // Legal council/board secretary can send confirmed resolutions to vote
    } else if (this.tokenService.hasRole('boardmember')) {
      this.userRole = 'boardmember';
      this.canViewDetails = true;
      this.canConfirmReject = false;
      this.canSendToVote = false;
    } else {
      this.userRole = 'Default';
      this.canViewDetails = false;
      this.canConfirmReject = false;
      this.canSendToVote = false;
    }
  }

  private setupBreadcrumbs(): void {
    if (this.fundId && this.fundId > 0) {
      this.updateBreadcrumb();
    } else {
      this.updateBreadcrumbWithFallback();
    }
  }

  private updateBreadcrumb(): void {
    this.breadcrumbItems = [
      // { label: 'BREADCRUMB.HOME', url: '/admin/dashboard' },
      { label: 'sidebar.funds', url: '/admin/investment-funds' },
      {
        label: 'INVESTMENT_FUNDS.FORM.FUND_NAME',
        url: `/admin/investment-funds/fund-details?id=${this.fundId}`,
      },
      {
        label: 'RESOLUTIONS.TITLE',
        url: `/admin/investment-funds/resolutions?fundId=${this.fundId}`,
      },
      {
        label: 'INVESTMENT_FUNDS.RESOLUTIONS.RESOLUTION_DETAILS',
        url: '',
        disabled: true,
      },
    ];
  }

  private updateBreadcrumbWithFallback(): void {
    // Fallback breadcrumb when fundId is not available
    this.breadcrumbItems = [
      { label: 'BREADCRUMB.HOME', url: '/admin/dashboard' },
      { label: 'BREADCRUMB.FUNDS', url: '/admin/investment-funds' },
      {
        label: 'BREADCRUMB.FUND_DETAILS',
        url: '/admin/investment-funds',
        disabled: true,
      },
      {
        label: 'RESOLUTIONS.TITLE',
        url: '/admin/investment-funds',
        disabled: true,
      },
      {
        label: 'INVESTMENT_FUNDS.RESOLUTIONS.RESOLUTION_DETAILS',
        url: '',
        disabled: true,
      },
    ];
  }

  getResolutionStatusById(): void {
    this.isLoading = true;
    this.hasError = false;

    this.resolutionService
      .GetResolutionStatusById(this.resolutionId)
      .subscribe({
        next: (response) => {
          debugger;
          this.isLoading = false;
          if (response.successed && response.data) {
            this.resolutionStatus = response.data;
          } else {
            this.handleError('RESOLUTIONS.FAILED_TO_LOAD');
          }
        },
        error: (error) => {
          this.isLoading = false;
          console.error('Error loading resolution details:', error);
          // this.handleError('RESOLUTIONS.FAILED_TO_LOAD');
        },
      });
  }

  loadResolutionDetails(): void {
    this.isLoading = true;
    this.hasError = false;

    this.resolutionService.getResolutionById(this.resolutionId).subscribe({
      next: (response) => {
        this.isLoading = false;
        if (response.successed && response.data) {
          this.resolution = response.data;

          this.getResolutionStatusById();
        } else {
          this.handleError('RESOLUTIONS.FAILED_TO_LOAD');
        }
      },
      error: (error) => {
        this.isLoading = false;
        console.error('Error loading resolution details:', error);
        // this.handleError('RESOLUTIONS.FAILED_TO_LOAD');
      },
    });
  }

  private handleError(messageKey: string): void {
    this.hasError = true;
    this.errorMessage = this.translateService.instant(messageKey);

    Swal.fire({
      title: this.translateService.instant('COMMON.ERROR'),
      text: this.errorMessage,
      icon: 'error',
      confirmButtonText: this.translateService.instant('COMMON.OK'),
    });
  }

  // Action methods
  onConfirmResolution(): void {
    if (!this.resolution || !this.canConfirmReject) return;

    Swal.fire({
      title: this.translateService.instant('RESOLUTIONS.CONFIRM_RESOLUTION'),
      text: this.translateService.instant(
        'RESOLUTIONS.CONFIRM_RESOLUTION_TEXT'
      ),
      imageUrl: 'assets/images/custom-icon.png',
      showCancelButton: true,
      customClass: {
        confirmButton: 'btn btn-primary',
        cancelButton: 'btn outline-btn',
      },
      reverseButtons: true,
      confirmButtonText: this.translateService.instant('RESOLUTIONS.CONFIRM'),
      cancelButtonText: this.translateService.instant('COMMON.CANCEL'),
    }).then((result) => {
      if (result.isConfirmed) {
        this.isLoading = true;

        this.resolutionsProxy.confirmResolution(this.resolution!.id).subscribe({
          next: (response) => {
            this.isLoading = false;
            if (response.successed) {
              this.errorModalService.showSuccess(
                'INVESTMENT_FUNDS.MEMBERS.SUCCESS_SAVED'
              );
              this.loadResolutionDetails();

              // Swal.fire({
              //   title: this.translateService.instant('COMMON.SUCCESS'),
              //   text: this.translateService.instant('INVESTMENT_FUNDS.SUCCESS_SAVED'),
              //   icon: 'success',
              //   timer: 2000,
              //   showConfirmButton: false
              // }).then(() => {
              //   // Reload the resolution details to show updated status
              //   this.loadResolutionDetails();
              // });
            } else {
              this.handleError('RESOLUTIONS.CONFIRM_FAILED');
            }
          },
          error: (error) => {
            this.isLoading = false;
            console.error('Error confirming resolution:', error);
            // this.handleError('RESOLUTIONS.CONFIRM_FAILED');
          },
        });
      }
    });
  }

  onRejectResolution(): void {
    if (!this.resolution || !this.canConfirmReject) return;

    Swal.fire({
      title: this.translateService.instant('RESOLUTIONS.REJECT_RESOLUTION'),
      input: 'textarea',
      inputLabel: this.translateService.instant('RESOLUTIONS.REJECTION_REASON'),
      inputPlaceholder: this.translateService.instant(
        'RESOLUTIONS.ENTER_REJECTION_REASON'
      ),
      inputValidator: (value) => {
        if (!value || value.trim().length === 0) {
          return this.translateService.instant(
            'RESOLUTIONS.REJECTION_REASON_REQUIRED'
          );
        }
        return null;
      },
        showCancelButton: true,
        customClass: {
          confirmButton: 'btn btn-primary',
          cancelButton: 'btn outline-btn',
        },
      confirmButtonText: this.translateService.instant('RESOLUTIONS.REJECT'),
      cancelButtonText: this.translateService.instant('COMMON.CANCEL'),
    }).then((result) => {
      if (result.isConfirmed && result.value) {
        this.isLoading = true;

        const rejectCommand = new RejectResolutionCommand({
          id: this.resolution!.id,
          rejectionReason: result.value.trim(),
        });

        this.resolutionsProxy
          .rejectResolution(this.resolution!.id, rejectCommand)
          .subscribe({
            next: (response) => {
              this.isLoading = false;
              if (response.successed) {
                this.errorModalService.showSuccess(
                  'INVESTMENT_FUNDS.MEMBERS.SUCCESS_SAVED'
                );
                this.loadResolutionDetails();

                // Swal.fire({
                //   title: this.translateService.instant('COMMON.SUCCESS'),
                //   text: this.translateService.instant('RESOLUTIONS.REJECTED_SUCCESSFULLY'),
                //   icon: 'success',
                //   timer: 2000,
                //   showConfirmButton: false
                // }).then(() => {
                //   // Reload the resolution details to show updated status
                //   this.loadResolutionDetails();
                // });
              } else {
                this.handleError('RESOLUTIONS.REJECT_FAILED');
              }
            },
            error: (error) => {
              this.isLoading = false;
              console.error('Error rejecting resolution:', error);
              // this.handleError('RESOLUTIONS.REJECT_FAILED');
            },
          });
      }
    });
  }

  onSendToVote(): void {
    if (!this.resolution || !this.canSendToVote) return;

    Swal.fire({
      title: this.translateService.instant('RESOLUTIONS.SEND_TO_VOTE'),
      text: this.translateService.instant('RESOLUTIONS.SEND_TO_VOTE_TEXT'),
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: '#007bff',
      cancelButtonColor: '#6c757d',
      confirmButtonText: this.translateService.instant(
        'RESOLUTIONS.SEND_TO_VOTE'
      ),
      cancelButtonText: this.translateService.instant('COMMON.CANCEL'),
    }).then((result) => {
      if (result.isConfirmed) {
        this.isLoading = true;

        this.resolutionsProxy.sendToVote(this.resolution!.id).subscribe({
          next: (response) => {
            this.isLoading = false;
            if (response.successed) {
              Swal.fire({
                title: this.translateService.instant('COMMON.SUCCESS'),
                text: this.translateService.instant(
                  'RESOLUTIONS.SUCCESS_SAVED'
                ),
                icon: 'success',
                timer: 2000,
                showConfirmButton: false,
              }).then(() => {
                // Reload the resolution details to show updated status
                this.loadResolutionDetails();
              });
            } else {
              this.handleError('RESOLUTIONS.SEND_TO_VOTE_FAILED');
            }
          },
          error: (error) => {
            this.isLoading = false;
            console.error('Error sending resolution to vote:', error);
            // this.handleError('RESOLUTIONS.SEND_TO_VOTE_FAILED');
          },
        });
      }
    });
  }

  onBreadcrumbClicked(item: IBreadcrumbItem): void {
    console.log('Breadcrumb clicked:', item);

    if (!item) {
      console.warn('Breadcrumb item is null or undefined');
      return;
    }

    if (item.disabled) {
      console.log('Breadcrumb item is disabled, ignoring click');
      return;
    }

    if (!item.url) {
      console.warn('Breadcrumb item has no URL:', item);
      return;
    }

    console.log('Navigating to:', item.url);
    this.router.navigateByUrl(item.url).catch((error) => {
      console.error('Navigation failed:', error);
    });
  }

  onBackToList(): void {
    this.router.navigate(['/admin/investment-funds/resolutions'], {
      queryParams: { fundId: this.fundId },
    });
  }

  getStatusDisplay(): string {
    if (!this.resolution) return '';
    return (
      this.resolution.resolutionStatus?.nameAr ||
      this.resolution.resolutionStatus?.nameEn ||
      this.translateService.instant(
        `RESOLUTIONS.STATUS_${this.resolution.status}`
      )
    );
  }

  shouldShowConfirmRejectButtons(): boolean {
    // Show confirm/reject buttons for fund manager when resolution is waiting for confirmation
    return (
      this.canConfirmReject &&
      this.resolution?.status === ResolutionStatusEnum._4
    ); // Waiting for confirmation status
  }

  shouldShowSendToVoteButton(): boolean {
    // Show send to vote button for legal council/board secretary when resolution is confirmed
    return (
      this.canSendToVote && this.resolution?.status === ResolutionStatusEnum._5
    ); // Confirmed status
  }

  // Status-specific UI methods
  shouldShowBasicInfo(): boolean {
    // Basic info is shown for all authorized users and statuses
    return true;
  }

  shouldShowResolutionItems(): boolean {
    // Resolution items are shown for completing data, waiting for confirmation, confirmed, and rejected statuses
    if (!this.resolution) return false;

    const statusesWithItems = [
      ResolutionStatusEnum._3, // Approved/Confirmed
      ResolutionStatusEnum._4, // Rejected/Cancelled
      ResolutionStatusEnum._5, // Voting in progress (completing data equivalent)
      ResolutionStatusEnum._6, // Not approved (waiting for confirmation equivalent)
    ];

    return statusesWithItems.includes(this.resolution.status);
  }

  shouldShowResolutionHistory(): boolean {
    // History is shown for all statuses except draft
    if (!this.resolution) return false;
    return this.resolution.status !== ResolutionStatusEnum._1; // Not draft
  }

  shouldShowRejectionReason(): boolean {
    // Rejection reason is shown only for rejected status
    if (!this.resolution) return false;
    return this.resolution.status === ResolutionStatusEnum._4; // Rejected/Cancelled
  }

  // User story specific access validation
  isJDWA588Access(): boolean {
    // JDWA-588: Fund manager viewing draft/pending/cancelled
    return !!(
      this.userRole === 'FundManager' &&
      this.resolution &&
      [
        ResolutionStatusEnum._1,
        ResolutionStatusEnum._2,
        ResolutionStatusEnum._4,
      ].includes(this.resolution.status)
    );
  }

  isJDWA584Access(): boolean {
    // JDWA-584: Legal council/board secretary viewing pending/cancelled
    return !!(
      this.userRole === 'LegalCouncil' &&
      this.resolution &&
      [ResolutionStatusEnum._2, ResolutionStatusEnum._4].includes(
        this.resolution.status
      )
    );
  }

  isJDWA593Access(): boolean {
    // JDWA-593: Fund manager viewing completing data/waiting for confirmation/confirmed/rejected
    return !!(
      this.userRole === 'FundManager' &&
      this.resolution &&
      [
        ResolutionStatusEnum._3,
        ResolutionStatusEnum._5,
        ResolutionStatusEnum._6,
      ].includes(this.resolution.status)
    );
  }

  isJDWA589Access(): boolean {
    // JDWA-589: Legal council/board secretary viewing completing data/waiting for confirmation/confirmed/rejected
    return !!(
      this.userRole === 'LegalCouncil' &&
      this.resolution &&
      [
        ResolutionStatusEnum._3,
        ResolutionStatusEnum._5,
        ResolutionStatusEnum._6,
      ].includes(this.resolution.status)
    );
  }

  formatDate(date: any): string {
    if (!date) return '';
    try {
      return new Date(date.toString()).toLocaleDateString('ar-SA');
    } catch {
      return '';
    }
  }

  // File operation methods
  onDownloadFile(): void {
    // TODO: Implement file download using FileManagementServiceProxy
    // Note: attachmentId property doesn't exist in SingleResolutionResponse
    console.log('Downloading file - feature not yet implemented');
  }

  onOpenFile(): void {
    // TODO: Implement file opening/preview
    // Note: attachmentId property doesn't exist in SingleResolutionResponse
    console.log('Opening file - feature not yet implemented');
  }

  onDownloadAttachment(item: any): void {
    // TODO: Implement attachment download
    console.log('Downloading attachment:', item);
  }

  onViewConflictMembers(item: any): void {
    const dialogRef = this.dialog.open(ConflictsPopupComponent, {
      width: '600px',
      data: item,
    });
    console.log('Viewing conflict members for item:', item);
  }

  ViewConflictMembers(item: ResolutionItemDto): void {
    if (!item.conflictMembers || item.conflictMembers.length === 0) {
      return;
    }

    const dialogData: ConflictMembersDialogData = {
      itemTitle: item.title || 'Resolution Item',
      conflictMembers: item.conflictMembers,
    };

    const dialogRef = this.dialog.open(ConflictsPopupComponent, {
      width: '500px',
      data: dialogData,
      disableClose: false,
    });

    dialogRef.afterClosed().subscribe(() => {
      // Dialog closed, no action needed
    });
  }

  getResolutionHistory(): any[] {
    // TODO: Implement resolution history API call
    // For now, return empty array as placeholder
    // This should be replaced with actual API call to get resolution history
    return [];
  }

  toggleExpand() {
    this.isExpanded = !this.isExpanded;
  }
  toggleExpandItems() {
    this.isExpandedItem = !this.isExpandedItem;
  }

  toggleExpandActions() {
    this.isExpandedAction = !this.isExpandedAction;
  }

  getStatusClass(statusId: number): string {
    switch (statusId) {
      case this.resolutionStatusEnum.Draft:
        return 'draft';
      case this.resolutionStatusEnum.Pending:
        return 'pending';
      case this.resolutionStatusEnum.CompletingData:
        return 'completing-data';
      case this.resolutionStatusEnum.WaitingForConfirmation:
        return 'waiting-for-confirmation';
      case this.resolutionStatusEnum.Confirmed:
        return 'confirmed';
      case this.resolutionStatusEnum.Rejected:
        return 'rejected';
      case this.resolutionStatusEnum.VotingInProgress:
        return 'voting-inProgress';
      case this.resolutionStatusEnum.Approved:
        return 'approved';
      case this.resolutionStatusEnum.NotApproved:
        return 'not-approved';
      case this.resolutionStatusEnum.Cancelled:
        return 'cancelled';
      default:
        return '';
    }
  }

  completeResolution(resolution: any) {
    this.router.navigate(['/admin/investment-funds/resolutions/edit'], {
      queryParams: { id: resolution.id, fundId: this.fundId },
    });
  }

  editResolution(resolution: any): void {
    // Navigate to edit page
    this.router.navigate(['/admin/investment-funds/resolutions/edit'], {
      queryParams: { id: resolution.id, fundId: this.fundId },
    });
  }

  cancel() {
    this.router.navigate(['/admin/investment-funds/resolutions'], {
      queryParams: { fundId: this.fundId },
    });
  }
}
