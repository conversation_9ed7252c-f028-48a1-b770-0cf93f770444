import { TokenService } from 'src/app/features/auth/services/token.service';
import { Component, Output, EventEmitter, OnInit, OnD<PERSON>roy, HostListener, ElementRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule } from '@angular/forms';
import { LanguageService } from '@core/gl-services/language-services/language.service';
import { LanguageEnum } from '@core/enums/language-enum/language-enum';
import { NotificationServiceProxy, NotificationDto, NotificationDtoPaginatedResult } from '@core/api/api.generated';
import { ScrollingModule, CdkVirtualScrollViewport } from '@angular/cdk/scrolling';
import { Subject, takeUntil, finalize } from 'rxjs';





@Component({
  selector: 'app-admin-layout-header',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    FormsModule,
    ScrollingModule
  ],
  templateUrl: './admin-layout-header.component.html',
  styleUrls: ['./admin-layout-header.component.scss']
})
export class AdminLayoutHeaderComponent implements OnInit, OnDestroy {
  @Output() menuToggle = new EventEmitter<void>();
  @Output() searchEvent = new EventEmitter<string>();
  @ViewChild('notificationDropdown', { static: false }) notificationDropdown!: ElementRef;
  @ViewChild('virtualScrollViewport', { static: false }) virtualScrollViewport!: CdkVirtualScrollViewport;

  // User and role properties
  storedRoles: string | null;
  notificationCount = 0;
  roleName: any;
  fullName: any;

  // Search properties
  searchQuery: string = '';
  currentLang: LanguageEnum = LanguageEnum.ar;

  // Notification dropdown properties
  isNotificationDropdownOpen = false;
  notifications: NotificationDto[] = [];
  isLoadingNotifications = false;
  isLoadingMoreNotifications = false;
  notificationError: string | null = null;

  // Pagination properties
  currentPage = 1;
  pageSize = 10;
  totalCount = 0;
  totalPages = 0;
  hasMoreNotifications = false;

  // Lifecycle management
  private destroy$ = new Subject<void>();

  constructor(
    private router: Router,
    private languageService: LanguageService,
    private TokenService: TokenService,
    private notificationServiceProxy: NotificationServiceProxy,
    private elementRef: ElementRef
  ) {
    this.initializeCurrentLanguage();
    this.languageService.currentLanguageEvent
      .pipe(takeUntil(this.destroy$))
      .subscribe((lang: LanguageEnum) => {
        this.currentLang = lang;
      });

    this.storedRoles = localStorage.getItem('roles');
    this.roleName = this.TokenService.getroles();
    this.fullName = this.TokenService.getFullName();
  }

  ngOnInit(): void {
    this.getUserNotificationUnreaded();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
  private initializeCurrentLanguage(): void {
    try {
      const storedLang = localStorage.getItem('lang');
      if (storedLang && storedLang !== '{}') {
        this.currentLang = JSON.parse(storedLang) as LanguageEnum;
      } else {
        this.currentLang = LanguageEnum.ar; // Default to Arabic
      }
    } catch (e) {
      this.currentLang = LanguageEnum.ar; // Default to Arabic
    }
  }
  changeLanguage(): void {
    const newLang = this.currentLang === LanguageEnum.en ? LanguageEnum.ar : LanguageEnum.en;
    this.currentLang = newLang;
    this.languageService.switchLang(this.currentLang,true);
  }

  selectLanguage(lang: string, event: Event): void {
    event.preventDefault();
    const newLang = lang === 'en' ? LanguageEnum.en : LanguageEnum.ar;
    this.currentLang = newLang;
    this.languageService.switchLang(this.currentLang, true);
  }

  getCurrentLanguageFlag(): string {
    return this.currentLang === LanguageEnum.en ? 'assets/images/en.png' : 'assets/images/ar.png';
  }

  getCurrentLanguageText(): string {
    return this.currentLang === LanguageEnum.en ? 'English' : 'عربي';
  }
  getUserNotificationUnreaded()
  {
    this.notificationServiceProxy.notitficationList().subscribe((res) => {
      this.notificationCount = res.data;
    });
  }
  toggleSidenav() {
    this.menuToggle.emit();
  }
  isDashboard(): boolean {
    return this.router.url.includes('/admin/dashboard');
  }
  onSearch(): void {
    if (this.searchQuery.trim()) {
      this.searchEvent.emit(this.searchQuery);
    }
  }

  // Navigate to user profile view
  navigateToProfile(): void {
    this.router.navigate(['/admin/user-management/my-profile']);
  }

  getPersonalPhotoUrl(): string {
    const photoUrl = this.TokenService.getPersonalPhotoUrl();
    return photoUrl || 'assets/images/5a88f6c30078d932a34b61c983a4185389144193.jpg';
  }

  // Notification dropdown methods
  toggleNotificationDropdown(): void {
    if (this.isNotificationDropdownOpen) {
      this.closeNotificationDropdown();
    } else {
      this.openNotificationDropdown();
    }
  }

  openNotificationDropdown(): void {
    this.isNotificationDropdownOpen = true;
    if (this.notifications.length === 0) {
      // Reset pagination state and load first page
      this.currentPage = 1;
      this.hasMoreNotifications = false;
      this.loadNotifications(1, false);
    }
  }

  closeNotificationDropdown(): void {
    this.isNotificationDropdownOpen = false;
  }

  // Method to refresh notifications (useful for retry functionality)
  refreshNotifications(): void {
    this.notifications = [];
    this.currentPage = 1;
    this.hasMoreNotifications = false;
    this.notificationError = null;
    this.loadNotifications(1, false);
  }

  loadNotifications(page: number = 1, append: boolean = false): void {
    // Prevent multiple simultaneous API calls
    if (this.isLoadingNotifications || this.isLoadingMoreNotifications) return;

    // Set appropriate loading state
    if (append) {
      this.isLoadingMoreNotifications = true;
    } else {
      this.isLoadingNotifications = true;
      this.notificationError = null;
    }

    this.notificationServiceProxy
      .unReadedNotificationList(page, this.pageSize, undefined, 'CreatedAt desc')
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => {
          this.isLoadingNotifications = false;
          this.isLoadingMoreNotifications = false;
        })
      )
      .subscribe({
        next: (response: NotificationDtoPaginatedResult) => {
          if (response.successed) {
            const newNotifications = response.data || [];

            if (append) {
              // Append new notifications to existing ones
              this.notifications = [...this.notifications, ...newNotifications];
            } else {
              // Replace notifications (initial load)
              this.notifications = newNotifications;
            }

            // Update pagination metadata
            this.currentPage = response.currentPage;
            this.totalCount = response.totalCount;
            this.totalPages = response.totalPages;
            this.hasMoreNotifications = response.hasNextPage;
          } else {
            this.notificationError = response.message || 'Failed to load notifications';
          }
        },
        error: (error) => {
          this.notificationError = 'Failed to load notifications';
        }
      });
  }

  // Infinite scroll functionality
  onScrolledIndexChange(index: number): void {
    // Check if we're near the end of the list and need to load more
    const threshold = 3; // Load more when 3 items from the end
    const isNearEnd = index >= this.notifications.length - threshold;

    if (isNearEnd && this.hasMoreNotifications && !this.isLoadingMoreNotifications) {
      const nextPage = this.currentPage + 1;
      this.loadNotifications(nextPage, true);
    }
  }

  markNotificationAsRead(notification: NotificationDto): void {
    if (!notification.isRead) {
      this.notificationServiceProxy.markAsRead(notification.id).subscribe({
        next: (response) => {
          if (response.successed) {
            notification.isRead = true;
            this.getUserNotificationUnreaded();
          }
        },
        error: (error) => {
          console.error('Error marking notification as read:', error);
        }
      });
    }
  }

  markAllAsRead(): void {
    // TODO: Call API to mark all as read
    this.notificationServiceProxy.markAllAsRead().subscribe({
      next: (response) => {
        if (response.successed) {
          this.getUserNotificationUnreaded();
          this.notifications.forEach(notification => {
            notification.isRead = true;
          });
        }
      },
      error: (error) => {
        console.error('Error marking all notifications as read:', error);
      }
    });
  }

  getNotificationIcon(notification: NotificationDto): string {
    // Return appropriate icon based on notification type
    switch (notification.notificationType) {
      case 8: // Fund notifications
        return 'assets/images/notify-green.svg';
      case 7: // User notifications
        return 'assets/images/notify-red.svg';
      default:
        return 'assets/images/notify-green.svg';
    }
  }

  getNotificationTime(createdAt: Date): string {
    const now = new Date();
    const notificationDate = new Date(createdAt);
    const diffInMinutes = Math.floor((now.getTime() - notificationDate.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) {
      return 'الآن';
    } else if (diffInMinutes < 60) {
      return `منذ ${diffInMinutes} دقيقة`;
    } else if (diffInMinutes < 1440) {
      const hours = Math.floor(diffInMinutes / 60);
      return `منذ ${hours} ساعة`;
    } else {
      const days = Math.floor(diffInMinutes / 1440);
      return `منذ ${days} يوم`;
    }
  }

  // TrackBy function for virtual scrolling performance
  trackByNotificationId(_index: number, notification: NotificationDto): number {
    return notification.id;
  }

  // Click outside handler
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event): void {
    if (this.isNotificationDropdownOpen &&
        !this.elementRef.nativeElement.contains(event.target)) {
      this.closeNotificationDropdown();
    }
  }
}
