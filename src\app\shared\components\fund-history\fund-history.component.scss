.fund-history-container {
  background: white;
  border-radius: 8px;
}

.tabs {
  padding: 12px 28px;
  display: flex;
  gap: 20px;
  margin-bottom: 22px;
  padding-bottom: 16px;
  background-color: #F8FAFC;
  border-top-right-radius: 10px;
  border-top-left-radius: 10px;

  .tab-button {
    display: flex;
    align-items: center;
    gap: 8px;
    background: none;
    border: none;
    padding: 8px 0;
    font-size: 14px;
    color: #666;
    cursor: pointer;
    transition: color 0.2s ease;
    font-weight: 400;

    img {
      width: 20px;
      height: 20px;
    }

    &.active {
      color: #00205a;
      font-weight: 500;
      position: relative;

      &:after {
        content: '';
        position: absolute;
        bottom: -17px;
        left: 0;
        right: 0;
        height: 3px;
        background-color: #00205a;
        border-radius: 4px;
      }
    }
  }
}

.timeline-container {
  padding: 20px;

  .btns_container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .loadmore-btn {
      font-weight: 400;
      color: #00205A;
      text-decoration: underline;

      background-color: transparent;
      border: 1px solid transparent;
      margin-bottom: 25px;

    }
    .reset-btn {
      background-color: #edf1f5;
      border: none;
      padding: 12px 24px;
      border-radius: 0 0 20px 20px;
      font-weight: bold;
      font-size: 16px;
      color: #002c5f;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      cursor: pointer;
      position: relative;
      width: 100%; // Optional: make it full width
      background-color: #EAEEF1;
      font-weight: 400;
      line-height: 20px;
     color: #00205A;
      
      img {
        width: 16px;
        height: 16px;
        object-fit: contain;
      }
    
      &:hover {
        background-color: #EAEEF1;
      }
    }
    
  }
}

.timeline-item {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  position: relative;
  border-bottom: 1px solid #EAEEF1;


  .timeline-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    background-color: #f8f9fa;

    img {
      width: 20px;
      height: 20px;
    }


  }

  .timeline-content {
    flex: 1;
    border-radius: 8px;
    padding: 0 16px;



    .title {
      font-size: 14px;
      font-weight: 400;
      color: #00205A;

      margin: 0;
    }

    .description {
      font-size: 12px;
      color: #424242;
      font-weight: 400;

      margin: 0 0 10px 0;
    }

    .status {
      display: flex;
      align-items: baseline;
      justify-content: center;
      gap: 8px;
      font-size: 12px;
      border-radius: 20px;
      padding: 0px 7px;
      height: 24px;
      display: flex;
      align-items: center;

      .circle {
        width: 12px;
        height: 12px;
        border-radius: 50%;
      }

      &.active {
        color: #28a745;

        i {
          color: #28a745;
        }
      }

      i {
        font-size: 12px;
      }
    }

    .status-new span {
      color: gray;
    }

    .status-under-construction {
      background-color: #E5EEFB;
      color: #2F80ED;

      span {
        background-color: #2F80ED;
      }
    }

    .status-waiting {
      background-color: #FDF1EB;
      color: #FF5F3D;

      span {
        background-color: #FF5F3D;
      }
    }

    .status-active {
      background-color: #F1FAF1;
      color: #27AE60;

      span {
        background-color: #27AE60;
      }
    }

    .status-exited {
      color: #828282;

      background-color: #E0E0E0;

      span {
        background-color: #828282;
      }
    }

    .timestamp {
      line-height: 16px;
      text-transform: capitalize;
      display: flex;
      align-items: center;
      font-size: 10px;
      color: #828282;
      justify-content: end;
      flex-direction: row-reverse;
      font-weight: 400;
      margin-bottom: 8px;


    }

    hr {
      color: #EAEEF1;
      border: 1px solid;
      width: 100%;
    }
  }

}
