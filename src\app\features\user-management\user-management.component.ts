import { ErrorModalService } from './../../core/services/error-modal.service';
import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { DateFormatterUtil } from '@shared/utils/date-formatter.util';
import { MatTableDataSource } from '@angular/material/table';
import { SelectionModel } from '@angular/cdk/collections';
import { MatDialog } from '@angular/material/dialog';
import { catchError, finalize } from 'rxjs/operators';
import { of } from 'rxjs';
import Swal from 'sweetalert2';

// User Management Error Handler
import { UserManagementErrorHandlerService } from './services/user-management-error-handler.service';

// Shared components
import { PageHeaderComponent } from '@shared/components/page-header/page-header.component';
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';
import { TableComponent } from '@shared/components/table/table.component';

// Core interfaces
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';
import {
  ActionDisplayMode,
  ITableColumn,
  TableActionEvent,
  SwitchToggleEvent,
} from '@core/gl-interfaces/I-table/i-table';
import { ColumnTypeEnum, DataHandlingType } from '@core/enums/column-type';

// User interfaces and components
import { IUser, IUserStatus, IUserFilters } from './interfaces/user.interface';
import { UserFilterDialogComponent } from './components/user-filter-dialog/user-filter-dialog.component';
import { UserManagementService } from '@shared/services/users/user-management.service';

@Component({
  selector: 'app-user-management',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    PageHeaderComponent,
    BreadcrumbComponent,
    TableComponent,
  ],
  providers: [UserManagementService],
  templateUrl: './user-management.component.html',
  styleUrl: './user-management.component.scss',
})
export class UserManagementComponent implements OnInit {
  // User data
  users: IUser[] = [];
  totalItems = 0;
  pageSize = 10;
  currentPage = 1;
  uniqueRoles: string[] = [];
  sigleRolesAvilavilty: any;
  // Sorting
  currentSortField = 'lastUpdateDate';
  currentSortDirection = 'desc';

  // Filters
  currentFilters: IUserFilters = {};
  mobileSearchTerm = '';

  // Loading state
  isLoading = false;

  // Table configuration
  columns: ITableColumn[] = [];
  displayedColumns: string[] = [];
  dataSource = new MatTableDataSource<IUser>([]);
  selection = new SelectionModel<IUser>(true, []);

  // Data handling
  sortingType = DataHandlingType.Backend;
  paginationType = DataHandlingType.Backend;

  // Breadcrumb
  breadcrumbItems: IBreadcrumbItem[] = [
    { label: 'BREADCRUMB.HOME', url: '/admin/dashboard' },
    { label: 'BREADCRUMB.USER_MANAGEMENT', active: true },
  ];

  constructor(
    private router: Router,
    private userManagementService: UserManagementService,
    private dialog: MatDialog,
    private translateService: TranslateService,
    private ErrorModalService: ErrorModalService,
    private userErrorHandler: UserManagementErrorHandlerService
  ) {}

  ngOnInit(): void {
    this.initializeTable();
    this.loadUsers();
    this.loadRoleAvailability();
    this.uniqueRoles = [
      'legalcouncil',
      'financecontroller',
      'compliancelegalmanagingdirector',
      'headofrealestate',
    ];
    console.log('Unique roles:', this.uniqueRoles);
  }

  private initializeTable(): void {
    this.columns = [
      {
        columnDef: 'fullName',
        header: 'USER_MANAGEMENT.COLUMNS.NAME',
        columnType: ColumnTypeEnum.Text,
        cell: (element: IUser) => element.fullName,
        isSortingBy: true,
      },
      // {
      //   columnDef: 'email',
      //   header: 'USER_MANAGEMENT.COLUMNS.EMAIL',
      //   columnType: ColumnTypeEnum.Text,
      //   cell: (element: IUser) => element.email,
      //   isSortingBy: true,
      // },
      {
        columnDef: 'mobile',
        header: 'USER_MANAGEMENT.COLUMNS.MOBILE',
        columnType: ColumnTypeEnum.Text,
        cell: (element: IUser) =>
          element.userName ? `${element.countryCode}${element.userName}` : '-',
        isSortingBy: true,
      },
      {
        columnDef: 'roles',
        header: 'USER_MANAGEMENT.COLUMNS.ROLE',
        columnType: ColumnTypeEnum.Text,
        cell: (element: IUser) => element.roles.join(', '),
        isSortingBy: true,
      },
      {
        columnDef: 'lastUpdateDate',
        header: 'USER_MANAGEMENT.COLUMNS.LAST_UPDATE_DATE',
        columnType: ColumnTypeEnum.Text,
        cell: (element: IUser) => this.formatDate(element.lastUpdateDate),
        isSortingBy: true,
      },
      {
        columnDef: 'isActive',
        header: 'USER_MANAGEMENT.COLUMNS.STATUS',
        columnType: ColumnTypeEnum.Status,
        cell: (element: IUser) => ({
          label: element.isActive
            ? this.translateService.instant('INVESTMENT_FUNDS.MEMBERS.ACTIVE')
            : this.translateService.instant(
                'INVESTMENT_FUNDS.MEMBERS.INACTIVE'
              ),
          class: this.getStatusClass(element.isActive),
        }),
        //  isSortingBy: true,
      },
      {
        columnDef: 'isActivee',
        header: 'USER_MANAGEMENT.COLUMNS.ACTIVATE',
        columnType: ColumnTypeEnum.Switch,
        cell: (element: IUser) => element.isActive,
        isSortingBy: false,
      },
      {
        columnDef: 'actions',
        header: 'USER_MANAGEMENT.COLUMNS.ACTIONS',
        columnType: ColumnTypeEnum.Actions,
        displayMode: ActionDisplayMode.Flex,
        cell: (element: IUser) => ({
          buttons: this.getActionsForUser(element),
        }),
      },
    ];

    this.displayedColumns = this.columns.map((col) => col.columnDef);
  }

  getStatusClass(status: boolean | undefined): string {
    switch (status) {
      case true:
        return 'status-green';
      case false:
        return 'status-orange';
      default:
        return 'status-blue';
    }
  }

  private formatDate(date: Date): string {
    return DateFormatterUtil.formatDate(date, this.translateService);
  }

  private getActionsForUser(user: IUser): any[] {
    const actions = [
      {
        label: 'USER_MANAGEMENT.ACTIONS.VIEW_DETAILS',
        action: 'view',
        iconSrc: 'assets/images/View.svg',
      },
      {
        label: 'USER_MANAGEMENT.ACTIONS.EDIT',
        action: 'edit',
        iconSrc: 'assets/images/edit.png',
      },
    ];

    // Add reset password action only for eligible users
    if (
      user.isActive &&
      user.registrationIsCompleted &&
      user.registrationMessageIsSent
    ) {
      actions.push({
        label: 'USER_MANAGEMENT.ACTIONS.RESET_PASSWORD',
        action: 'resetPassword',
        iconSrc: 'assets/images/reset.png',
      });
    }

    // Add resend action only for users who haven't completed registration
    if (
      user.isActive &&
      !user.registrationIsCompleted &&
      user.registrationMessageIsSent
    ) {
      actions.push({
        label: 'USER_MANAGEMENT.ACTIONS.RESEND',
        action: 'resendMessage',
        iconSrc: 'assets/images/Resend.svg',
      });
    }

    return actions;
  }

  loadUsers(): void {
    this.isLoading = true;

    // Build API parameters
    const role = this.currentFilters.role || undefined;
    const isActive =
      this.currentFilters.status === IUserStatus.Active
        ? true
        : this.currentFilters.status === IUserStatus.Inactive
        ? false
        : undefined;
    const name = this.currentFilters.name || undefined;
    const search =
      this.currentFilters.searchTerm || this.mobileSearchTerm || undefined;
    const orderBy =
      this.currentSortField && this.currentSortDirection
        ? `${this.currentSortField} ${this.currentSortDirection}`
        : undefined;

    this.userManagementService
      .getUserList(
        role,
        isActive,
        name,
        this.currentPage,
        this.pageSize,
        search,
        orderBy
      )
      .subscribe((response: any) => {
        if (response && response.data) {
          this.users = response.data;
          this.dataSource.data = this.users;
          this.totalItems = response.totalCount || 0;
        } else {
          this.users = [];
          this.dataSource.data = this.users;
          this.totalItems = 0;
        }
        this.isLoading = false;
      });
  }

  /**
   * Loads role availability data to check for single-holder role conflicts
   */
  private loadRoleAvailability(): void {
    this.userManagementService.CheckRoleAvailability().subscribe({
      next: (response: any) => {
        if (response && response.data) {
          this.sigleRolesAvilavilty = response.data;
          console.log('Role availability loaded:', this.sigleRolesAvilavilty);
        }
      },
      error: (error) => {
        console.error('Error loading role availability:', error);
        // Set default values if API fails
        this.sigleRolesAvilavilty = {
          legalCouncilHasActiveUser: false,
          financeControllerHasActiveUser: false,
          complianceLegalManagingDirectorHasActiveUser: false,
          headOfRealEstateHasActiveUser: false,
        };
      },
    });
  }

  // Table event handlers
  onTableAction(event: TableActionEvent): void {
    const { action, row } = event;
    this.onUserAction({ action, user: row as IUser });
  }

  onSwitchToggle(event: SwitchToggleEvent): void {
    const { row, newValue } = event;
    this.onUserStatusToggled({ user: row as IUser, newStatus: newValue });
  }

  onUserAction(event: { action: string; user: IUser }): void {
    const { action, user } = event;

    switch (action) {
      case 'view':
        this.viewUserDetails(user);
        break;
      case 'edit':
        this.editUser(user);
        break;
      case 'resetPassword':
        this.resetUserPassword(user);
        break;
      case 'resendMessage':
        this.resendMessage(user);
        break;
      default:
        console.log('Unknown action:', action);
    }
  }

  async onUserStatusToggled(event: {
    user: IUser;
    newStatus: boolean;
  }): Promise<void> {
    const { user, newStatus } = event;
    // If activating user, check for single-holder role conflicts

    const messageKey = newStatus
      ? 'USER_MANAGEMENT.CONFIRM.ACTIVATE_USER'
      : 'USER_MANAGEMENT.CONFIRM.DEACTIVATE_USER';
    // background-color: #bdbdbd;
    // color: #4f4f4f;
    Swal.fire({
      text: this.translateService.instant(messageKey, { name: user.fullName }),
      imageUrl: 'assets/images/custom-icon.png',
      showCancelButton: true,
      customClass: {
        confirmButton: 'btn btn-primary',
        cancelButton: 'btn outline-btn',
      },
      confirmButtonText: this.translateService.instant('COMMON.YES'),
      cancelButtonText: this.translateService.instant('COMMON.NO'),
    }).then(async (result) => {
      if (result.isConfirmed) {
        if (
          newStatus &&
          (await this.discardUserActivateIfHasRoleConflict(user))
        ) {
          // User cancelled activation due to role conflicts
          this.loadUsers(); // Revert the switch
          return;
        } else {
          this.isLoading = true;
          const activateUserBody = {
            userId: user.id,
            activate: newStatus,
            sendNotification: true,
          };
          this.userManagementService
            .activateUser(activateUserBody)
            .pipe(
              catchError((error) => {
                this.loadUsers();
                return of(null);
              }),
              finalize(() => {
                this.isLoading = false;
              })
            )
            .subscribe((response) => {
              if (response && response.successed) {
                // Show success message using the error handler service
                this.userErrorHandler.showSuccess(
                  newStatus ? 'activate' : 'deactivate',
                  user.fullName
                );
                this.loadUsers(); // Reload the user list to reflect changes
                this.loadRoleAvailability(); // Refresh role availability data
              }
            });
        }
      } else {
        // Revert the switch if user cancels
        this.loadUsers();
      }
    });
  }

  onSortChanged(event: { active: string; direction: string }): void {
    debugger;
    this.currentSortField = event.active;
    this.currentSortDirection = event.direction;
    this.loadUsers();
  }

  onPageChanged(event: any): void {
    this.currentPage = event.pageIndex + 1;
    this.pageSize = event.pageSize;
    this.loadUsers();
  }

  onFiltersChanged(filters: IUserFilters): void {
    this.loadUsers();
  }

  // Search and filter methods
  onSearch(searchTerm: string): void {
    this.mobileSearchTerm = searchTerm;
    this.currentFilters.searchTerm = searchTerm || undefined;
    this.currentPage = 1;
    this.loadUsers();
  }

  openAdvancedFilters(): void {
    const dialogRef = this.dialog.open(UserFilterDialogComponent, {
      width: '500px',
      data: { ...this.currentFilters },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        // Merge with mobile filter
        this.currentFilters = {
          ...result,
          mobileNo: this.mobileSearchTerm || undefined,
        };
        this.currentPage = 1;
        this.loadUsers();
      }
    });
  }

  clearAllFilters(): void {
    this.mobileSearchTerm = '';
    this.currentFilters = {};
    this.currentPage = 1; // Reset to first page when filters are cleared
    this.loadUsers();
  }

  hasActiveFilters(): boolean {
    return (
      Object.keys(this.currentFilters).length > 0 || !!this.mobileSearchTerm
    );
  }

  // Action implementations
  private viewUserDetails(user: IUser): void {
    console.log('Viewing user details:', user);
    // Navigate to user details page
    this.router.navigate(['/admin/user-management/details', user.id]);
  }

  private editUser(user: IUser): void {
    console.log('Editing user:', user);
    // Navigate to edit user form
    this.router.navigate(['/admin/user-management/edit', user.id]);
  }

  private resetUserPassword(user: IUser): void {
    Swal.fire({
      text: this.translateService.instant(
        'USER_MANAGEMENT.CONFIRM.RESET_PASSWORD_MESSAGE',
        { name: user.fullName }
      ),
      imageUrl: 'assets/images/custom-icon.png',
      showCancelButton: true,
      customClass: {
        confirmButton: 'btn btn-primary',
        cancelButton: 'btn outline-btn',
      },
      confirmButtonText: this.translateService.instant('COMMON.YES'),
      cancelButtonText: this.translateService.instant('COMMON.NO'),
    }).then((result) => {
      if (result.isConfirmed) {
        this.isLoading = true;

        // Call reset password API
        const resetPasswordData = {
          userId: user.id,
        };

        this.userManagementService
          .setNewPasswordForUser(resetPasswordData)
          .pipe(
            catchError((error) => {
              this.ErrorModalService.showError(error.error.message);
              return of(null);
            }),
            finalize(() => {
              this.isLoading = false;
            })
          )
          .subscribe((response) => {
            if (response && response.successed) {
              this.ErrorModalService.showSuccess(
                'USER_MANAGEMENT.SUCCESS.RESET_PASSWORD_SUCCESS'
              );
              this.loadUsers(); // Reload the user list
            }
          });
      }
    });
  }

  private resendMessage(user: IUser): void {
    Swal.fire({
      text: this.translateService.instant(
        'USER_MANAGEMENT.CONFIRM.RESEND_MESSAGE',
        { name: user.fullName }
      ),
      imageUrl: 'assets/images/custom-icon.png',
      showCancelButton: true,
      customClass: {
        confirmButton: 'btn btn-primary',
        cancelButton: 'btn outline-btn',
      },
      confirmButtonText: this.translateService.instant('COMMON.YES'),
      cancelButtonText: this.translateService.instant('COMMON.NO'),
    }).then((result) => {
      if (result.isConfirmed) {
        this.isLoading = true;

        // Call resend message API
        const resendData = {
          userId: user.id,
        };

        this.userManagementService
          .resendRegistrationMessage(resendData)
          .pipe(
            catchError((error) => {
              return of(null);
            }),
            finalize(() => {
              this.isLoading = false;
            })
          )
          .subscribe((response) => {
            if (response && response.successed) {
              this.ErrorModalService.showSuccess(
                'USER_MANAGEMENT.SUCCESS.RESEND_MESSAGE_SUCCESS'
              );
              this.loadUsers(); // Reload the user list
            }
          });
      }
    });
  }

  onAddUser(): void {
    console.log('Add new user');
    this.router.navigate(['/admin/user-management/create']);
  }

  /**
   * Checks for single-holder role conflicts when activating a user
   * Returns true if user cancelled activation, false if should proceed
   */
  private async discardUserActivateIfHasRoleConflict(
    user: IUser
  ): Promise<boolean> {
    // Only check if user has exactly one role and it's a single-holder role
    if (!user.enRoles || user.enRoles.length !== 1) {
      return false; // No conflict, proceed with activation
    }

    const userRole = user.enRoles[0].toLowerCase();

    // Check if the role is a single-holder role
    if (!this.uniqueRoles.includes(userRole)) {
      return false; // Not a single-holder role, proceed with activation
    }

    // Check if role availability data is loaded
    if (!this.sigleRolesAvilavilty) {
      return false;
    }

    // Map role names to availability flags
    const roleAvailabilityMap: { [key: string]: string } = {
      legalcouncil: 'legalCouncilHasActiveUser',
      financecontroller: 'financeControllerHasActiveUser',
      compliancelegalmanagingdirector:
        'complianceLegalManagingDirectorHasActiveUser',
      headofrealestate: 'headOfRealEstateHasActiveUser',

      legalcounciluserfullname: 'legalCouncilUserFullName',
      financecontrolleruserfullname: 'financeControllerUserFullName',
      compliancelegalmanagingdirectoruserfullname:
        'complianceLegalManagingDirectorUserFullName',
      headofrealestateuserfullname: 'headOfRealEstateUserFullName',
    };

    const availabilityFlag = roleAvailabilityMap[userRole];
    const userNames: string[] = [];
    // Check if there's already an active user with this role
    if (
      availabilityFlag &&
      this.sigleRolesAvilavilty[availabilityFlag] === true
    ) {
      // Show confirmation dialog
      userNames.push(
        this.sigleRolesAvilavilty[
          roleAvailabilityMap[userRole + 'userfullname']
        ]
      );
      const shouldProceed = await this.showRoleAvailabilityConfirmation(
        [userRole],
        userNames
      );
      return !shouldProceed; // Return true if user cancelled (don't proceed)
    }

    return false; // No conflict, proceed with activation
  }

  /**
   * Shows confirmation dialog for roles that are already taken
   */
  private showRoleAvailabilityConfirmation(
    conflictingRoles: string[],
    userNames: string[]
  ): Promise<boolean> {
    return new Promise((resolve) => {
      const rolesList = conflictingRoles.join(', ');

      Swal.fire({
        title: this.translateService.instant(
          'USER_MANAGEMENT.ROLE_AVAILABILITY.TITLE'
        ),
        text: this.translateService.instant(
          'USER_MANAGEMENT.ROLE_AVAILABILITY.MESSAGE',
          {
            roleName: this.translateService.instant(rolesList),
            userName: userNames,
          }
        ),
        imageUrl: 'assets/images/custom-icon.png',
        showCancelButton: true,
        customClass: {
          confirmButton: 'btn btn-primary',
          cancelButton: 'btn outline-btn',
        },
        confirmButtonText: this.translateService.instant('COMMON.YES'),
        cancelButtonText: this.translateService.instant('COMMON.NO'),
      }).then((result) => {
        resolve(result.isConfirmed);
      });
    });
  }
}
